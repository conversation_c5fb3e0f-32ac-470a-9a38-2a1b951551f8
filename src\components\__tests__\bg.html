<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI-Powered UI Hero Section</title>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <style>
        /* --- Basic Setup & Variables --- */
        :root {
            --bg-color: #0D0628;
            --primary-color: #7646FF;
            --secondary-color: #A594FD;
            --text-color: #E0E0E0;
            --text-muted-color: #BDBDBD;
            --font-family: 'Poppins', sans-serif;
            --transition-speed: 0.3s;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--bg-color);
            color: var(--text-color);
            overflow-x: hidden; /* Prevents horizontal scroll */
        }

        /* --- Animations --- */
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .text-update-animation {
            animation: fadeOutIn 0.6s ease-in-out;
        }

        @keyframes fadeOutIn {
            0% { opacity: 1; transform: translateY(0); }
            50% { opacity: 0; transform: translateY(-10px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        /* --- Hero Section Layout --- */
        .hero-section {
            position: relative;
            width: 100%;
            min-height: 100vh;
            padding: 2rem 6%;
            display: flex;
            flex-direction: column;
            background: radial-gradient(circle at 10% 20%, rgba(118, 70, 255, 0.15), transparent 35%), 
                        radial-gradient(circle at 90% 80%, rgba(165, 148, 253, 0.1), transparent 40%),
                        var(--bg-color);
            overflow: hidden;
        }

        .background-lines {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3ClinearGradient id='grad1' x1='0%25' y1='0%25' x2='100%25' y2='0%25'%3E%3Cstop offset='0%25' style='stop-color:rgba(255,255,255,0.05);stop-opacity:1' /%3E%3Cstop offset='100%25' style='stop-color:rgba(255,255,255,0);stop-opacity:1' /%3E%3C/linearGradient%3E%3C/defs%3E%3Cpath d='M-300,150 C-100,50 100,250 300,150 S 700,50 900,150 S 1300,250 1500,150 S 1900,50 2100,150' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,300 C-100,200 100,400 300,300 S 700,200 900,300 S 1300,400 1500,300 S 1900,200 2100,300' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,450 C-100,350 100,550 300,450 S 700,350 900,450 S 1300,550 1500,450 S 1900,350 2100,450' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,600 C-100,500 100,700 300,600 S 700,500 900,600 S 1300,700 1500,600 S 1900,500 2100,600' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3Cpath d='M-300,750 C-100,650 100,850 300,750 S 700,650 900,750 S 1300,850 1500,750 S 1900,650 2100,750' stroke='url(%23grad1)' stroke-width='1' fill='none'/%3E%3C/svg%3E");
            background-size: cover; opacity: 0.5; z-index: 0; animation: fadeIn 2s ease-in-out;
        }

        /* --- Navigation --- */
        nav { display: flex; justify-content: space-between; align-items: center; width: 100%; z-index: 100; }
        .logo { animation: fadeIn 1s ease-out; }
        .nav-links-container { display: flex; align-items: center; }
        .nav-links { display: flex; list-style: none; gap: 2.5rem; animation: fadeIn 1s ease-out 0.2s backwards; }
        .nav-links a { text-decoration: none; color: var(--text-muted-color); font-weight: 500; font-size: 1rem; transition: color var(--transition-speed); position: relative; padding-bottom: 5px; }
        .nav-links a::after { content: ''; position: absolute; bottom: 0; left: 0; width: 0; height: 2px; background: var(--secondary-color); transition: width var(--transition-speed) ease-out; }
        .nav-links a:hover::after, .nav-links a.active::after { width: 100%; }
        .nav-links a:hover, .nav-links a.active { color: white; }
        .nav-buttons { display: flex; align-items: center; gap: 1rem; animation: fadeIn 1s ease-out 0.4s backwards; }
        .btn-login { text-decoration: none; color: var(--text-color); font-weight: 500; padding: 0.6rem 1.2rem; border-radius: 8px; transition: background-color var(--transition-speed); }
        .btn-login:hover { background-color: rgba(255, 255, 255, 0.1); }
        .btn-signup { text-decoration: none; color: white; font-weight: 500; padding: 0.6rem 1.2rem; border-radius: 8px; background: linear-gradient(90deg, var(--secondary-color), var(--primary-color)); transition: transform var(--transition-speed), box-shadow var(--transition-speed); display: inline-block; }
        .btn-signup:hover { transform: translateY(-3px); box-shadow: 0 4px 20px rgba(118, 70, 255, 0.4); }
        .btn-signup i { margin-left: 0.5rem; transition: transform var(--transition-speed); }
        .btn-signup:hover i { transform: translateX(3px); }
        .menu-toggle { display: none; background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer; z-index: 101; }

        /* --- Main Hero Content --- */
        .hero-content { display: flex; align-items: center; justify-content: space-between; flex-grow: 1; gap: 2rem; position: relative; z-index: 5; }
        .hero-text { flex-basis: 50%; max-width: 600px; }
        .hero-text > * { animation: fadeInUp 0.8s ease-out forwards; opacity: 0; }
        .hero-text .welcome-text { animation-delay: 0.2s; }
        .hero-text .headline-container { animation-delay: 0.4s; }
        .hero-text .description-container { animation-delay: 0.6s; }
        .hero-text .search-form { animation-delay: 0.8s; }
        .hero-text .trusted-by { animation-delay: 1s; }
        .welcome-text { font-size: 1.1rem; font-weight: 500; color: var(--secondary-color); margin-bottom: 1rem; }
        
        .headline-container, .description-container { display: flex; align-items: flex-start; gap: 1rem; }
        
        .hero-text h1 { font-size: clamp(2.5rem, 5vw, 3.5rem); font-weight: 700; line-height: 1.2; margin-bottom: 1.5rem; flex-grow: 1; }
        .description { font-size: 1rem; color: var(--text-muted-color); margin-bottom: 2rem; max-width: 450px; flex-grow: 1; }
        
        .btn-gemini {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            width: 36px; height: 36px;
            border-radius: 50%;
            display: flex; align-items: center; justify-content: center;
            font-size: 1.2rem;
            cursor: pointer;
            transition: transform 0.3s, box-shadow 0.3s;
            flex-shrink: 0;
            margin-top: 5px;
        }
        .btn-gemini:hover { transform: scale(1.1) rotate(15deg); box-shadow: 0 4px 15px rgba(118, 70, 255, 0.4); }
        .btn-gemini:disabled { cursor: not-allowed; background: #555; }

        /* --- Search Form --- */
        .search-form { display: flex; align-items: center; background-color: rgba(255, 255, 255, 0.05); border-radius: 12px; padding: 0.5rem; max-width: 500px; margin-bottom: 2rem; border: 1px solid rgba(255, 255, 255, 0.1); transition: border-color var(--transition-speed); }
        .search-form:focus-within { border-color: var(--secondary-color); }
        .search-icon { font-size: 1.2rem; color: var(--text-muted-color); margin: 0 1rem; }
        .search-form input { flex-grow: 1; background: transparent; border: none; outline: none; color: white; font-size: 1rem; font-family: var(--font-family); }
        .search-form input::placeholder { color: var(--text-muted-color); }
        .search-form button { background-color: var(--primary-color); color: white; border: none; border-radius: 8px; padding: 0.8rem 1.8rem; font-weight: 600; cursor: pointer; transition: background-color var(--transition-speed); }
        .search-form button:hover { background-color: var(--secondary-color); }

        /* --- Trusted By Section --- */
        .trusted-by { display: flex; align-items: center; flex-wrap: wrap; gap: 1rem; color: var(--text-muted-color); }
        .rating { display: flex; align-items: center; gap: 0.5rem; }
        .rating .fa-star, .rating .fa-star-half-stroke { color: #FFC107; }
        .rating span { margin-left: 0.5rem; }

        /* --- Hero Image --- */
        .hero-image { flex-basis: 50%; display: flex; align-items: center; justify-content: center; perspective: 1000px; }
        .hero-image img { max-width: 100%; width: 650px; height: auto; animation: fadeInUp 1s ease-out 0.6s forwards; opacity: 0; transition: transform 0.1s linear; }

        /* --- Scroll Buttons --- */
        .scroll-buttons { position: absolute; bottom: 2rem; right: 6%; display: flex; flex-direction: column; gap: 0.5rem; z-index: 10; animation: fadeIn 1s ease-out 1.2s backwards; }
        .scroll-btn { width: 40px; height: 40px; border-radius: 50%; border: 1px solid rgba(255, 255, 255, 0.2); background: rgba(255, 255, 255, 0.1); color: white; cursor: pointer; transition: background-color var(--transition-speed), transform var(--transition-speed); display: flex; justify-content: center; align-items: center; }
        .scroll-btn:hover { background: rgba(255, 255, 255, 0.2); transform: scale(1.1); }

        /* --- Modals and Loaders --- */
        .modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.7); backdrop-filter: blur(5px); display: flex; justify-content: center; align-items: center; z-index: 1000; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s; }
        .modal-overlay.show { opacity: 1; visibility: visible; }
        .loading-spinner { width: 50px; height: 50px; border: 5px solid rgba(255,255,255,0.3); border-top-color: var(--secondary-color); border-radius: 50%; animation: spin 1s linear infinite; }
        .error-message { position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); background-color: #ff4d4d; color: white; padding: 1rem 2rem; border-radius: 8px; z-index: 1001; opacity: 0; visibility: hidden; transition: opacity 0.3s, visibility 0.3s; }
        .error-message.show { opacity: 1; visibility: visible; }

        /* --- Responsive Design --- */
        @media (max-width: 1024px) {
            .hero-section { padding: 2rem 4%; }
            .hero-content { flex-direction: column; text-align: center; gap: 3rem; margin-top: 4rem; }
            .hero-text { max-width: 100%; display: flex; flex-direction: column; align-items: center; }
            .headline-container, .description-container { justify-content: center; }
            .description { max-width: 500px; }
            .search-form { max-width: 500px; width: 100%; }
            .hero-image img { width: 500px; transform: none; }
            .scroll-buttons { right: 4%; }
        }

        @media (max-width: 768px) {
            .nav-links-container { position: fixed; top: 0; right: 0; width: 70%; height: 100vh; background: rgba(13, 6, 40, 0.95); backdrop-filter: blur(10px); flex-direction: column; justify-content: center; align-items: center; transform: translateX(100%); transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1); }
            .nav-links-container.active { transform: translateX(0); }
            .nav-links { flex-direction: column; gap: 2rem; text-align: center; }
            .nav-links a { font-size: 1.2rem; }
            .nav-buttons { flex-direction: column; margin-top: 2rem; gap: 1.5rem; }
            .btn-login, .btn-signup { width: 150px; text-align: center; }
            .menu-toggle { display: block; }
            .hero-content { margin-top: 2rem; }
            .hero-text h1 { font-size: 2.2rem; }
            .trusted-by { justify-content: center; }
            .hero-image { display: none; }
            .scroll-buttons { display: none; }
        }
    </style>
</head>
<body>

    <header class="hero-section" id="hero">
        <div class="background-lines"></div>
        <nav>
            <div class="logo">
                <a href="#hero" aria-label="Go to homepage">
                    <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 0L27.0711 2.92893L37.0711 12.9289L40 20L37.0711 27.0711L27.0711 37.0711L20 40L12.9289 37.0711L2.92893 27.0711L0 20L2.92893 12.9289L12.9289 2.92893L20 0Z" fill="url(#paint0_linear_1_2)"/><path d="M20 5L24.3301 7.5L32.5 17.5L35 20L32.5 22.5L24.3301 32.5L20 35L15.6699 32.5L7.5 22.5L5 20L7.5 17.5L15.6699 7.5L20 5Z" fill="url(#paint1_linear_1_2)"/><defs><linearGradient id="paint0_linear_1_2" x1="20" y1="0" x2="20" y2="40" gradientUnits="userSpaceOnUse"><stop stop-color="#A594FD"/><stop offset="1" stop-color="#7646FF"/></linearGradient><linearGradient id="paint1_linear_1_2" x1="20" y1="5" x2="20" y2="35" gradientUnits="userSpaceOnUse"><stop stop-color="white" stop-opacity="0.2"/><stop offset="1" stop-color="white" stop-opacity="0"/></linearGradient></defs></svg>
                </a>
            </div>
            <div class="nav-links-container" id="navLinksContainer">
                <ul class="nav-links">
                    <li><a href="#" class="active">Home</a></li><li><a href="#">About Us</a></li><li><a href="#">Services</a></li><li><a href="#">Our Story</a></li><li><a href="#">Contact Us</a></li>
                </ul>
                <div class="nav-buttons">
                    <a href="#" class="btn-login">Log In</a><a href="#" class="btn-signup">Sign Up <i class="fa-solid fa-arrow-right"></i></a>
                </div>
            </div>
            <button class="menu-toggle" id="menuToggle" aria-label="Toggle navigation menu"><i class="fa-solid fa-bars"></i></button>
        </nav>

        <main class="hero-content">
            <div class="hero-text">
                <p class="welcome-text">Welcome To BezzDesign</p>
                
                <div class="headline-container">
                    <h1 id="heroHeadline">The Ultimate Guide To Use A Unique UI/UX</h1>
                    <button class="btn-gemini" id="generateHeadlineBtn" aria-label="Generate new headline with AI">✨</button>
                </div>

                <div class="description-container">
                    <p class="description" id="heroDescription">Lorem ipsum dolor sit amet consectetur. Tincidunt iaculis luctus leo in mattis sagittis facilisis adipiscing.</p>
                    <button class="btn-gemini" id="generateDescriptionBtn" aria-label="Generate new description with AI">✨</button>
                </div>
                
                <form class="search-form">
                    <i class="fa-solid fa-magnifying-glass search-icon"></i><input type="email" placeholder="Enter your E-Mail Here" required><button type="submit">ENTER NOW</button>
                </form>

                <div class="trusted-by">
                    <p>Trusted by 50k+ users</p>
                    <div class="rating">
                        <i class="fa-solid fa-star" aria-hidden="true"></i><i class="fa-solid fa-star" aria-hidden="true"></i><i class="fa-solid fa-star" aria-hidden="true"></i><i class="fa-solid fa-star" aria-hidden="true"></i><i class="fa-solid fa-star-half-stroke" aria-hidden="true"></i>
                        <span aria-label="4.1 out of 5 stars">4.1/5 (14k Reviews)</span>
                    </div>
                </div>
            </div>
            <div class="hero-image">
                <img src="https://i.ibb.co/L5rKxT8/flower.png" alt="Blue decorative flower" id="heroImage">
            </div>
        </main>

        <div class="scroll-buttons">
            <button class="scroll-btn" aria-label="Scroll up"><i class="fa-solid fa-arrow-up"></i></button><button class="scroll-btn" aria-label="Scroll down"><i class="fa-solid fa-arrow-down"></i></button>
        </div>
    </header>

    <!-- Modal for Loading State -->
    <div class="modal-overlay" id="loadingModal">
        <div class="loading-spinner"></div>
    </div>

    <!-- Error Message -->
    <div class="error-message" id="errorMessage">
        Oops! Something went wrong. Please try again.
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- Element Selections ---
            const menuToggle = document.getElementById('menuToggle');
            const navLinksContainer = document.getElementById('navLinksContainer');
            const heroImage = document.getElementById('heroImage');
            const heroSection = document.getElementById('hero');
            const generateHeadlineBtn = document.getElementById('generateHeadlineBtn');
            const generateDescriptionBtn = document.getElementById('generateDescriptionBtn');
            const heroHeadline = document.getElementById('heroHeadline');
            const heroDescription = document.getElementById('heroDescription');
            const loadingModal = document.getElementById('loadingModal');
            const errorMessage = document.getElementById('errorMessage');

            // --- Mobile Menu Toggle ---
            if (menuToggle && navLinksContainer) {
                menuToggle.addEventListener('click', () => {
                    navLinksContainer.classList.toggle('active');
                    const icon = menuToggle.querySelector('i');
                    icon.classList.toggle('fa-bars');
                    icon.classList.toggle('fa-times');
                });
            }

            // --- Parallax Effect for Hero Image ---
            if (window.innerWidth > 1024) {
                heroSection.addEventListener('mousemove', (e) => {
                    const { clientX, clientY } = e;
                    const x = (clientX - window.innerWidth / 2) / 25;
                    const y = (clientY - window.innerHeight / 2) / 25;
                    if (heroImage) {
                       heroImage.style.transform = `rotateY(${-x / 2}deg) rotateX(${y / 2}deg) translateZ(20px)`;
                    }
                });
                heroSection.addEventListener('mouseleave', () => {
                    if (heroImage) {
                        heroImage.style.transform = 'rotateY(0) rotateX(0) translateZ(0)';
                    }
                });
            }

            // --- Gemini API Functions ---

            // Function to show a temporary error message
            function showErrorMessage() {
                errorMessage.classList.add('show');
                setTimeout(() => {
                    errorMessage.classList.remove('show');
                }, 3000);
            }

            // Generic function to call the Gemini API
            async function callGeminiAPI(prompt) {
                loadingModal.classList.add('show');
                setGeminiButtonsDisabled(true);

                const apiKey = ""; // Will be provided by the environment
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
                
                const payload = {
                    contents: [{
                        role: "user",
                        parts: [{ text: prompt }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        maxOutputTokens: 100,
                    }
                };

                try {
                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });

                    if (!response.ok) {
                        throw new Error(`API Error: ${response.status}`);
                    }

                    const result = await response.json();
                    
                    if (result.candidates && result.candidates.length > 0) {
                        return result.candidates[0].content.parts[0].text.trim();
                    } else {
                        throw new Error("No content generated.");
                    }
                } catch (error) {
                    console.error("Gemini API call failed:", error);
                    showErrorMessage();
                    return null;
                } finally {
                    loadingModal.classList.remove('show');
                    setGeminiButtonsDisabled(false);
                }
            }
            
            function setGeminiButtonsDisabled(disabled) {
                generateHeadlineBtn.disabled = disabled;
                generateDescriptionBtn.disabled = disabled;
            }

            // Animate text update
            function updateTextWithAnimation(element, newText) {
                // Remove any characters that could break the HTML (like stray asterisks)
                const sanitizedText = newText.replace(/[*_`]/g, '');
                element.classList.add('text-update-animation');
                setTimeout(() => {
                    element.textContent = sanitizedText;
                    element.addEventListener('animationend', () => {
                        element.classList.remove('text-update-animation');
                    }, { once: true });
                }, 300); // Halfway through the animation
            }

            // Event listener for generating a new headline
            generateHeadlineBtn.addEventListener('click', async () => {
                const prompt = `Generate one alternative, catchy headline for a guide about UI/UX. The current headline is: "${heroHeadline.textContent}". Make it concise and powerful.`;
                const newHeadline = await callGeminiAPI(prompt);
                if (newHeadline) {
                    updateTextWithAnimation(heroHeadline, newHeadline);
                }
            });

            // Event listener for generating a new description
            generateDescriptionBtn.addEventListener('click', async () => {
                const prompt = `Based on the headline "${heroHeadline.textContent}", write a compelling one-sentence description for a UI/UX guide. The current description is: "${heroDescription.textContent}". Make it engaging and clear.`;
                const newDescription = await callGeminiAPI(prompt);
                if (newDescription) {
                    updateTextWithAnimation(heroDescription, newDescription);
                }
            });
        });
    </script>

</body>
</html>
