@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset all margins and padding to remove gaps */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  margin: 0;
  padding: 0;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Remove default margins from root element */
#root {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

/* Custom Color Palette - Purple Theme */
:root {
  --bg-color: #0D0628;          /* Very dark, almost black purple */
  --primary-color: #7646FF;     /* Bright, saturated violet */
  --secondary-color: #A594FD;   /* Lighter, softer lavender */
  --text-color: #E0E0E0;        /* Light grey for high readability */
  --text-muted-color: #BDBDBD;  /* Slightly darker grey for secondary text */

  /* Legacy color variables for backward compatibility */
  --color-primary: #7646FF;     /* Updated to match new primary */
  --color-secondary: #A594FD;   /* Updated to match new secondary */
  --color-accent: #0D0628;      /* Updated to match new background */
  --color-background: #0D0628;  /* Updated to match new background */
  --color-text: #E0E0E0;        /* Updated to match new text */
  --color-white: #FFFFFF;       /* Pure white */
}

/* Hero Section Background Styling */
.hero-section {
    background: radial-gradient(circle at 10% 20%, rgba(118, 70, 255, 0.15), transparent 35%),
                radial-gradient(circle at 90% 80%, rgba(165, 148, 253, 0.1), transparent 40%),
                var(--bg-color);
    position: relative;
    overflow: hidden;
}

.background-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-opacity='0.03'%3E%3Cpolygon fill='%23A594FD' points='50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40'/%3E%3C/g%3E%3C/svg%3E");
    background-size: cover;
    opacity: 0.5;
    z-index: 0;
}




/* Meteor Effect Styles - Updated for Purple Theme */
.meteor {
  position: absolute;
  right: -100px;
  width: 2px;
  height: 2px;
  background: linear-gradient(45deg, rgba(118, 70, 255, 0.8), rgba(165, 148, 253, 0.4));
  border-radius: 50%;
  animation: meteorFall infinite linear;
  opacity: 0;
  filter: blur(0.5px);
}

.meteor::before {
  content: '';
  position: absolute;
  top: -1px;
  right: 2px;
  width: 80px;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(118, 70, 255, 0.6), rgba(165, 148, 253, 0.3), transparent);
  border-radius: 50%;
  filter: blur(1px);
  transform: rotate(-45deg);
  transform-origin: left center;
}

.meteor::after {
  content: '';
  position: absolute;
  top: -2px;
  right: 4px;
  width: 120px;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(118, 70, 255, 0.4), rgba(165, 148, 253, 0.2), transparent);
  border-radius: 50%;
  filter: blur(2px);
  transform: rotate(-45deg);
  transform-origin: left center;
}

@keyframes meteorFall {
  0% {
    transform: translateX(0) translateY(0) scale(0);
    opacity: 0;
  }
  5% {
    opacity: 1;
    transform: translateX(-20px) translateY(20px) scale(1);
  }
  50% {
    opacity: 0.8;
    transform: translateX(-200px) translateY(200px) scale(1.2);
  }
  100% {
    transform: translateX(-400px) translateY(400px) scale(0.5);
    opacity: 0;
  }
}

/* NavBar Animation */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideDown {
  animation: slideDown 0.3s ease-out;
}

/* Video Strip Styles */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Film Strip Animation */
@keyframes filmRoll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-20px);
  }
}

.film-strip-holes {
  animation: filmRoll 2s ease-in-out infinite alternate;
}

/* Enhanced Film Strip Holes */
@keyframes filmHolesLeft {
  0% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(-20px) scale(1);
    opacity: 0.8;
  }
}

@keyframes filmHolesRight {
  0% {
    transform: translateY(-20px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-10px) scale(1.1);
    opacity: 1;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.8;
  }
}

.film-strip-holes-left {
  animation: filmHolesLeft 3s ease-in-out infinite;
}

.film-strip-holes-right {
  animation: filmHolesRight 3s ease-in-out infinite;
}

/* Enhanced Continuous Video Strip Scroll */
@keyframes videoStripScrollEnhanced {
  0% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-33.33%) scale(1.02);
  }
  100% {
    transform: translateY(-66.66%) scale(1);
  }
}

.video-strip-scroll-enhanced {
  animation: videoStripScrollEnhanced 25s ease-in-out infinite;
  will-change: transform;
}

/* Floating Particles Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0) translateX(0) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translateY(-20px) translateX(10px) rotate(90deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) translateX(-5px) rotate(180deg);
    opacity: 1;
  }
  75% {
    transform: translateY(-30px) translateX(15px) rotate(270deg);
    opacity: 0.5;
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Pulse Slow Animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

/* Rotated Video Strip Container */
.video-strip-container {
  transform: rotate(45deg);
  transform-origin: center center;
}

/* Enhanced Video Frame Effects */
.video-frame-enhanced {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
  position: relative;
}

.video-frame-enhanced::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg,
    rgba(118, 70, 255, 0.3) 0%,
    rgba(165, 148, 253, 0.2) 25%,
    rgba(118, 70, 255, 0.3) 50%,
    rgba(165, 148, 253, 0.2) 75%,
    rgba(118, 70, 255, 0.3) 100%);
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: -1;
}

.video-frame-enhanced:hover::before {
  opacity: 1;
  animation: borderGlow 2s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Radial Gradient Background */
.bg-gradient-radial {
  background: radial-gradient(circle at center, var(--tw-gradient-stops));
}

/* Enhanced Video Container */
.video-container-enhanced {
  position: relative;
  overflow: hidden;
  border-radius: 12px;
}

.video-container-enhanced::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(118, 70, 255, 0.4),
    transparent);
  transition: left 0.5s ease;
}

.video-container-enhanced:hover::after {
  left: 100%;
}

/* Enhanced Scrolling Video Frame Optimizations */
.scrolling-video-canvas {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Dark Theme Video Loading Indicator - Updated for Purple Theme */
.video-loading-dark {
  background: rgba(13, 6, 40, 0.95);
  border: 2px solid rgba(118, 70, 255, 0.3);
  backdrop-filter: blur(10px);
}

/* Smooth 60fps Animation Class */
.smooth-60fps {
  animation-timing-function: linear;
  will-change: transform, opacity;
  transform: translateZ(0);
  image-rendering: optimizeSpeed;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Enhanced Film Strip Dark Theme - Updated for Purple Theme */
.film-strip-dark {
  background: linear-gradient(45deg,
    rgba(13, 6, 40, 0.98) 0%,
    rgba(20, 10, 50, 0.95) 25%,
    rgba(25, 15, 60, 0.98) 50%,
    rgba(20, 10, 50, 0.95) 75%,
    rgba(13, 6, 40, 0.98) 100%);
  box-shadow:
    0 0 30px rgba(118, 70, 255, 0.1),
    inset 0 0 20px rgba(13, 6, 40, 0.8);
}

/* Scan Line Animation */
@keyframes scan-line {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-scan-line {
  animation: scan-line 2s ease-in-out infinite;
}

/* Progress Bar Animation */
@keyframes progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

.animate-progress {
  animation: progress 10s linear infinite;
}

/* Enhanced Border Animation */
@keyframes border-3 {
  border-width: 3px;
}

/* Glitch Effect for Active Video */
@keyframes glitch {
  0%, 100% {
    transform: translate(0);
  }
  20% {
    transform: translate(-2px, 2px);
  }
  40% {
    transform: translate(-2px, -2px);
  }
  60% {
    transform: translate(2px, 2px);
  }
  80% {
    transform: translate(2px, -2px);
  }
}

.animate-glitch {
  animation: glitch 0.3s ease-in-out infinite;
}

/* 3D Transform Effects */
.transform-3d {
  transform-style: preserve-3d;
  perspective: 1000px;
}

/* Video Strip Container Enhancements */
.video-strip-container {
  transform-style: preserve-3d;
  perspective: 2000px;
}

.video-strip-container:hover {
  transform: rotate(45deg) scale(1.02);
}

/* Video Frame Hover Effects */
.video-frame {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.video-frame:hover {
  transform: translateX(-5px);
}

/* Custom Video Controls */
.video-container {
  position: relative;
  overflow: hidden;
}

.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 49%, rgba(118, 70, 255, 0.1) 50%, transparent 51%);
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-container:hover::before {
  opacity: 1;
}

/* Seamless Loop Scrolling Animation */
@keyframes seamlessScrollUp {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}

.seamless-scroll-up {
  animation: seamlessScrollUp 20s linear infinite;
}

/* Performance Optimized Video Frames */
.video-frame-optimized {
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Dark Theme Enhancements */
.dark-video-overlay {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
}

.purple-glow {
  box-shadow:
    0 0 10px rgba(118, 70, 255, 0.3),
    0 0 20px rgba(118, 70, 255, 0.2),
    0 0 30px rgba(118, 70, 255, 0.1);
}

/* Legacy cyan-glow class updated to purple theme */
.cyan-glow {
  box-shadow:
    0 0 10px rgba(118, 70, 255, 0.3),
    0 0 20px rgba(118, 70, 255, 0.2),
    0 0 30px rgba(118, 70, 255, 0.1);
}

/* Hardware Acceleration Classes */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}